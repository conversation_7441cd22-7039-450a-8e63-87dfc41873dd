package org.acme.appointmentconfig.repositories


import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped

import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlot
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ApplicationScoped
class AppointmentSlotRepo : PanacheRepositoryBase<AppointmentSlot, Int> {
    fun findOpenSlots(
        consId: Long, date: LocalDate
    ): List<AppointmentSlot> = find(
        "consultantId = ?1 and slotDate = ?2 and availability = 'OPEN'",
        consId, date
    )
        .list()

    fun findSlotByConsultantDateAndNumber(
        consultantId: UUID,
        date: LocalDate,
        slotNumber: Long
    ): AppointmentSlot? = find(
        "consultantId = ?1 and slotDate = ?2 and slotNumber = ?3",
        consultantId, date, slotNumber
    ).firstResult()


    fun findByConsultantSlotAndDate(consultantId: UUID, slotNumber: Int, slotDate: LocalDate): AppointmentSlot? {
        return find("consultantId = ?1 and slotNumber = ?2 and slotDate = ?3", consultantId, slotNumber, slotDate)
            .firstResult()
    }
}

