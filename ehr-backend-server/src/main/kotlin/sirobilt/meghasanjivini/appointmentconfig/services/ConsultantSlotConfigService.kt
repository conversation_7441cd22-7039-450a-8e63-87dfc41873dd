package org.acme.appointmentconfig.services

import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import jakarta.ws.rs.NotFoundException
import jakarta.ws.rs.WebApplicationException
import jakarta.ws.rs.core.Response
import sirobilt.meghasanjivini.appointmentconfig.ConsultantSlotConfig
import sirobilt.meghasanjivini.appointmentconfig.ConsultantSlotConfigDto
import sirobilt.meghasanjivini.appointmentconfig.ConsultantSlotConfigRepo
import sirobilt.meghasanjivini.appointmentconfig.dto.ConsultantSlotConfigResponseDto
import java.util.UUID

@ApplicationScoped
@Transactional
class ConsultantSlotConfigService(
    private val configRepo: ConsultantSlotConfigRepo
) {

    /** Create a new slot‐config and return the saved DTO (with generated id). */
    fun createConfig(dto: ConsultantSlotConfigDto): ConsultantSlotConfigDto {


        val existing = configRepo.findtByConsultantId(dto.consultantId)
        if (existing != null) {
            throw WebApplicationException(
                "A slot‐config for consultant ${dto.consultantId} already exists",
                Response.Status.CONFLICT
            )
        }

        val entity = ConsultantSlotConfig().apply {
            consultantId  = dto.consultantId
            daysOfWeek    = dto.daysOfWeek
            startTime     = dto.startTime!!
            endTime       = dto.endTime!!
            slotDuration  = dto.slotDuration
            effectiveFrom = dto.effectiveFrom!!
            effectiveTo   = dto.effectiveTo
        }
        configRepo.persist(entity)
        // map back to DTO
        return ConsultantSlotConfigDto().also {

            it.consultantId   = entity.consultantId
            it.daysOfWeek     = entity.daysOfWeek
            it.startTime      = entity.startTime
            it.endTime        = entity.endTime
            it.slotDuration   = entity.slotDuration
            it.effectiveFrom  = entity.effectiveFrom
            it.effectiveTo    = entity.effectiveTo
        }
    }

    /** Update an existing config; throws if not found. */
    fun updateConfig(consultantId: UUID, dto: ConsultantSlotConfigDto): ConsultantSlotConfigDto {
        val entity = configRepo.findtByConsultantId(consultantId)
            ?: throw IllegalArgumentException("Slot config not found for consultant: $consultantId")

        // apply changes
        entity.apply {
            this.consultantId = dto.consultantId
            this.daysOfWeek = dto.daysOfWeek
            this.startTime = dto.startTime!!
            this.endTime = dto.endTime!!
            this.slotDuration = dto.slotDuration
            this.effectiveFrom = dto.effectiveFrom!!
            this.effectiveTo = dto.effectiveTo
        }
        // Panache auto-flushes, now map back
        return ConsultantSlotConfigDto().also {

            it.consultantId   = entity?.consultantId!!
            it.daysOfWeek     = entity.daysOfWeek
            it.startTime      = entity.startTime
            it.endTime        = entity.endTime
            it.slotDuration   = entity.slotDuration
            it.effectiveFrom  = entity.effectiveFrom
            it.effectiveTo    = entity.effectiveTo
        }
    }

    fun getConfigsForConsultant(consultantId: UUID): List<ConsultantSlotConfigResponseDto> {
        val entities = configRepo.list("consultantId = ?1", consultantId)
        return entities.map { entity ->
            ConsultantSlotConfigResponseDto(
                scheduleId = entity.scheduleId!!, // Guaranteed after DB insert
                consultantId = entity.consultantId,
                daysOfWeek = entity.daysOfWeek,
                startTime = entity.startTime,
                endTime = entity.endTime,
                slotDuration = entity.slotDuration,
                effectiveFrom = entity.effectiveFrom,
                effectiveTo = entity.effectiveTo
            )
        }
    }

    /** Fetch one by id. */
    fun getConfigById(consultantId: UUID): ConsultantSlotConfigDto {
        val entity = configRepo.findtByConsultantId(consultantId)
        return ConsultantSlotConfigDto().also {

            it.consultantId   = entity?.consultantId!!
            it.daysOfWeek     = entity.daysOfWeek
            it.startTime      = entity.startTime
            it.endTime        = entity.endTime
            it.slotDuration   = entity.slotDuration
            it.effectiveFrom  = entity.effectiveFrom
            it.effectiveTo    = entity.effectiveTo
        }
    }

    fun getAllConfigs(): List<ConsultantSlotConfigResponseDto> =
        configRepo.listAll().map { toDto(it) }

    private fun toDto(entity: ConsultantSlotConfig): ConsultantSlotConfigResponseDto =
        ConsultantSlotConfigResponseDto(
            scheduleId    = entity.scheduleId!!,
            consultantId  = entity.consultantId,
            daysOfWeek    = entity.daysOfWeek,
            startTime     = entity.startTime,
            endTime       = entity.endTime,
            slotDuration  = entity.slotDuration,
            effectiveFrom = entity.effectiveFrom,
            effectiveTo   = entity.effectiveTo
        )


    fun deleteConfigByConfigId(configId: Long) {
        val config = configRepo
            .findByIdOptional(configId)
            .orElseThrow { NotFoundException("No slot‐config $configId") }
        configRepo.delete(config)
    }

    fun deleteConfigByConsultantId(consultantId: UUID) {
        // bulk delete by consultantId; returns number of rows deleted
        val deletedCount = configRepo.delete("consultantId = ?1", consultantId)
        if (deletedCount == 0L) {
            throw NotFoundException("No slot‐configs found for consultant $consultantId")
        }
    }
}