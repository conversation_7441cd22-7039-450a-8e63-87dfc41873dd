package sirobilt.meghasanjivini.appointment

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import sirobilt.meghasanjivini.common.enums.AppointmentType
import sirobilt.meghasanjivini.common.enums.RecurringPattern
import sirobilt.meghasanjivini.masterdata.model.Facility
import sirobilt.meghasanjivini.patientregistration.model.Patient
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "appointments")
class       AppointmentEntity : PanacheEntityBase {
    @Id
    @Column(name = "appointment_id", length = 50)
    lateinit var id: String


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id")
    lateinit var patient: Patient

    @Column(name = "provider_id", nullable = false)
    lateinit var providerId: String


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facility_id")
    lateinit var facilityId: Facility

    @Column(name = "appointment_date", nullable = false)
    lateinit var appointmentDate: LocalDateTime

    @Column(name = "start_time", nullable = false)
    lateinit var startTime: LocalDateTime

    @Column(name = "end_time", nullable = false)
    lateinit var endTime: LocalDateTime

    @Column(name = "duration", nullable = false)
    var duration: Int = 0

    @Column(name = "type", length = 20, nullable = false)
    @Enumerated(EnumType.STRING)
    lateinit var type: AppointmentType

    @Column(name = "status", length = 20, nullable = false)
    var status: String = "Scheduled"

    @Column(name = "priority", length = 20, nullable = false)
    var priority: String = "Normal"

    @Column(name = "title", length = 200)
    var title: String? = null

    @Column(name = "description")
    var description: String? = null

    @Column(name = "notes")
    var notes: String? = null

    @Column(name = "reason", length = 500)
    var reason: String? = null

    @Column(name = "is_recurring")
    var isRecurring: Boolean = false

    @Column(name = "recurring_pattern", length = 20)
    var recurringPattern: RecurringPattern? = null

    @Column(name = "recurring_end_date")
    var recurringEndDate: String? = null

    @Column(name = "parent_appointment_id", length = 50)
    var parentAppointmentId: String? = null

    @Column(name = "external_system_id", length = 100)
    var externalSystemId: String? = null

    @Column(name = "external_system_name", length = 50)
    var externalSystemName: String? = null

    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "created_by", length = 50)
    lateinit var createdBy: String

    @Column(name = "updated_by", length = 50)
    lateinit var updatedBy: String

    @Column(name = "slot_id", nullable = true)
    var slotId: Int? = null

}
