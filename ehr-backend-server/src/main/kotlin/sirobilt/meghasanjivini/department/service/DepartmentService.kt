package sirobilt.meghasanjivini.department.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import jakarta.ws.rs.NotFoundException
import sirobilt.meghasanjivini.common.exception.ConflictException
import sirobilt.meghasanjivini.department.dto.DepartmentDTO
import sirobilt.meghasanjivini.department.dto.DepartmentOperatingHoursDTO
import sirobilt.meghasanjivini.department.model.Department
import sirobilt.meghasanjivini.department.model.DepartmentOperatingHours
import sirobilt.meghasanjivini.department.repository.DepartmentRepository
import sirobilt.meghasanjivini.department.repository.ProviderDepartmentMappingRepository
import java.time.LocalDateTime
import java.util.UUID

@ApplicationScoped
class DepartmentService(
    private val departmentRepository: DepartmentRepository,

    private val providerDepartmentMappingRepository: ProviderDepartmentMappingRepository
) {

    fun getAll(): List<DepartmentDTO> =
        departmentRepository.listAll().map { dept ->
            val doctorCount = providerDepartmentMappingRepository.countMappedDoctors(dept.departmentId)
            dept.toDTO(doctorCount)
        }

    fun findById(id: String): DepartmentDTO {
        val department = departmentRepository.findById(id) ?: throw NotFoundException("Department not found")
        val doctorCount = providerDepartmentMappingRepository.countMappedDoctors(department.departmentId)
        return department.toDTO(doctorCount)
    }

    @Transactional
    fun create(dto: DepartmentDTO): Department {

        if (departmentRepository.find("name", dto.name).firstResult() != null) {
            throw ConflictException("Department with name '${dto.name}' already exists.")
        }
        val dept = Department().apply {
            departmentId = UUID.randomUUID().toString()
            facilityId = dto.facilityId
            name = dto.name
            code = dto.code
            description = dto.description
            headOfDepartment = dto.headOfDepartment
            phoneNumber = dto.phoneNumber
            email = dto.email
            location = dto.location
            isActive = dto.isActive
            isEmergencyDepartment = dto.isEmergencyDepartment
        }

        dto.operatingHours?.let { hoursDtoList ->
            val operatingHoursEntities = hoursDtoList.map { hoursDto ->
                DepartmentOperatingHours().apply {
                    dayOfWeek = hoursDto.dayOfWeek
                    isOperating = hoursDto.isOperating
                    startTime = hoursDto.startTime
                    endTime = hoursDto.endTime
                    breakStartTime = hoursDto.breakStartTime
                    breakEndTime = hoursDto.breakEndTime


                    // Set the owning side of the relationship
                    department = dept
                }
            }
            dept.operatingHours = operatingHoursEntities
        }

        departmentRepository.persist(dept)
        return dept
    }



    @Transactional
    fun update(id: String, dto: DepartmentDTO): DepartmentDTO {
        val department = findById(id)
        department.apply {
            name = dto.name
            description = dto.description
            phoneNumber = dto.phoneNumber
            email = dto.email
            location = dto.location
            isActive = dto.isActive
            headOfDepartment = dto.headOfDepartment
            code = dto.code
            isEmergencyDepartment = dto.isEmergencyDepartment

        }
        return department
    }


    @Transactional
    fun delete(id: String) {
        val entity = departmentRepository.findById(id)
            ?: throw NotFoundException("Department with id=$id not found")

        providerDepartmentMappingRepository.delete("departmentId", id)

        departmentRepository.delete(entity)
    }

    fun countMappedDoctors(departmentId: String): Long {
        return providerDepartmentMappingRepository.countActiveDoctorsByDepartment(departmentId)
    }


    fun Department.toDTO(doctorCount: Int): DepartmentDTO = DepartmentDTO(
        departmentId = this.departmentId,
        facilityId = this.facilityId,
        name = this.name,
        code = this.code,
        description = this.description,
        headOfDepartment = this.headOfDepartment,
        phoneNumber = this.phoneNumber,
        email = this.email,
        location = this.location,
        isActive = this.isActive,
        isEmergencyDepartment = this.isEmergencyDepartment,
        operatingHours = this.operatingHours.map { it.toDTO() },
        mappedDoctorCount = doctorCount
    )

    fun DepartmentOperatingHours.toDTO(): DepartmentOperatingHoursDTO = DepartmentOperatingHoursDTO(
        dayOfWeek = this.dayOfWeek,
        isOperating = this.isOperating,
        startTime = this.startTime,
        endTime = this.endTime,
        breakStartTime = this.breakStartTime,
        breakEndTime = this.breakEndTime
    )
}