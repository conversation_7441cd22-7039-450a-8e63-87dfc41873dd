package sirobilt.meghasanjivini.department.controllers

import jakarta.ws.rs.*
import jakarta.ws.rs.core.*
import sirobilt.meghasanjivini.department.dto.DepartmentDTO
import sirobilt.meghasanjivini.department.service.DepartmentService

@Path("/departments")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class DepartmentController(
    private val service: DepartmentService
) {
    @GET
    fun getAll(): Response = Response.ok(service.getAll()).build()

    @GET
    @Path("/{id}")
    fun getOne(@PathParam("id") id: String): Response = Response.ok(service.findById(id)).build()

    @POST
    fun create(dto: DepartmentDTO): Response = Response.status(Response.Status.CREATED).entity(service.create(dto)).build()

    @PUT
    @Path("/{id}")
    fun update(@PathParam("id") id: String, dto: DepartmentDTO): Response = Response.ok(service.update(id, dto)).build()

    @DELETE
    @Path("/{id}")
    fun delete(@PathParam("id") id: String): Response {
        service.delete(id)
        return Response.noContent().build()
    }

    @GET
    @Path("/{id}/doctor-count")
    fun countDoctors(@PathParam("id") id: String): Response {
        val count = service.countMappedDoctors(id)
        return Response.ok(mapOf("count" to count)).build()
    }

}