package sirobilt.meghasanjivini.masterdata

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "doctors")
class Doctor : PanacheEntityBase {

    @Id
    @Column(name = "doctor_id", columnDefinition = "UUID")
    lateinit var doctorId: UUID


    @Column(name = "first_name", nullable = false, length = 150)
    lateinit var firstName: String

    @Column(name = "last_name", nullable = false, length = 150)
    lateinit var lastName: String

    @Column(name = "middle_name", nullable = true, length = 150)
    var middleName: String? = null

    @Column(name = "specialization", nullable = false, length = 150)
    lateinit var specialization: String

    @Column(name = "role_type", nullable = false, length = 150)
    lateinit var roleType: String

    @Column(name = "qualification", nullable = false, length = 150)
    lateinit var qualification: String

    @Column(name = "gender", nullable = false, length = 20)
    lateinit var gender: String

    @Column(name = "date_of_birth", nullable = false)
    lateinit var dateOfBirth: LocalDate

    @Column(name = "mobile_number", nullable = false, length = 15)
    lateinit var mobileNumber: String

    @Column(name = "email", nullable = false, length = 100)
    lateinit var email: String

    @Column(name = "registration_number", nullable = false, length = 50, unique = true)
    lateinit var registrationNumber: String

    @Column(name = "registration_state", length = 50)
    var registrationState: String? = null

    @Column(name = "years_of_experience", nullable = false)
    var yearsOfExperience: Int = 0

    @Column(name = "telemedicine_ready", nullable = false)
    var telemedicineReady: Boolean = false

    @Column(name = "languages_spoken", columnDefinition = "text[]")
    var languagesSpoken: List<String> = listOf()

    @Column(name = "is_active", nullable = false)
    var isActive: Boolean = false

    @Embedded
    lateinit var address: Address

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime? = null
}
