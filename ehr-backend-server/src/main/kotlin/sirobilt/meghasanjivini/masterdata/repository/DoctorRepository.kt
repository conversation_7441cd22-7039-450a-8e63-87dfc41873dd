package sirobilt.meghasanjivini.masterdata.repository


import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.common.enums.SlotAvailability
import sirobilt.meghasanjivini.masterdata.model.Doctor
import java.util.*
import io.quarkus.panache.common.Parameters;
import java.time.LocalDate


@ApplicationScoped
class DoctorRepository : PanacheRepository<Doctor> {

    fun findById(id: UUID): Doctor? =
        find("doctorId", id).firstResult()

    fun findAllActive(): List<Doctor> =
        find("isActive", true).list()

    fun listBySpecialization(specializationId: String): List<Doctor> =
        list("specialization.specializationId", specializationId)

    // Custom delete method for UUID
    fun deleteById(id: UUID): Boolean {
        return delete("doctorId = ?1", id) > 0
    }

    fun findAvailableDoctorsForDate(date: LocalDate): List<Doctor> {
        val dayOfWeek = date.dayOfWeek

        return find(
            """
            FROM Doctor d
            WHERE d.isActive = true
              AND d.doctorId IN (
                  SELECT c.consultantId
                  FROM ConsultantSlotConfig c
                  JOIN c.daysOfWeek dow
                  WHERE dow = :dayOfWeek
                    AND (c.effectiveTo IS NULL OR c.effectiveTo >= :date)
                    AND c.effectiveFrom <= :date
                    AND c.consultantId IN (
                        SELECT s.consultantId
                        FROM AppointmentSlot s
                        WHERE s.availability = :availability
                          AND s.slotDate = :date
                          AND s.dayOfWeek = :dayOfWeek
                    )
              )
            """,
            mapOf(
                "dayOfWeek" to dayOfWeek,
                "availability" to SlotAvailability.OPEN,
                "date" to date
            )
        ).list()
    }




}