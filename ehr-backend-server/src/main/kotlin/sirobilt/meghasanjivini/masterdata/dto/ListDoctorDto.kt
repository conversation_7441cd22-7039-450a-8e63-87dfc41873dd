package sirobilt.meghasanjivini.masterdata.dto


import sirobilt.meghasanjivini.masterdata.model.Doctor
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

data class ListDoctorDto(
    val doctorId: UUID,
    val firstName: String,
    val lastName: String,
    val fullName: String,
    val middleName: String? = null,
    val specialization: String,
    val roleType: String,
    val qualification: String,
    val gender: String,
    val dateOfBirth: LocalDate,
    val mobileNumber: String,
    val email: String,
    val registrationNumber: String,
    val registrationState: String?,
    val yearsOfExperience: Int,
    val telemedicineReady: Boolean,
    val languagesSpoken: List<String>,
    val isActive: Boolean,
    val address: AddressDto,
    val createdAt: LocalDateTime? = null
) {
    companion object {
        fun fromEntity(doc: Doctor): ListDoctorDto {
            val fullName = listOfNotNull(doc.firstName, doc.middleName, doc.lastName).joinToString(" ")

            return ListDoctorDto(
                doctorId = doc.doctorId,
                firstName = doc.firstName,
                lastName = doc.lastName,
                middleName = doc.middleName,
                fullName = fullName,
                specialization = doc.specialization,
                roleType = doc.roleType,
                qualification = doc.qualification,
                gender = doc.gender,
                dateOfBirth = doc.dateOfBirth,
                mobileNumber = doc.mobileNumber,
                email = doc.email,
                registrationNumber = doc.registrationNumber,
                registrationState = doc.registrationState,
                yearsOfExperience = doc.yearsOfExperience,
                telemedicineReady = doc.telemedicineReady,
                languagesSpoken = doc.languagesSpoken,
                isActive = doc.isActive,
                address = AddressDto.fromEmbeddable(doc.address),
                createdAt = doc.createdAt
            )
        }
    }

}
