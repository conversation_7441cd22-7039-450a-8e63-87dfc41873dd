package sirobilt.meghasanjivini.masterdata.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import sirobilt.meghasanjivini.common.converter.StringListConverter
import sirobilt.meghasanjivini.common.model.Address
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "facility_staff")
class FacilityStaff : PanacheEntityBase {

    @Id
    @Column(name = "staff_id", columnDefinition = "UUID")
    var staffId: UUID = UUID.randomUUID()

    @Column(name = "first_name", nullable = false, length = 150)
    lateinit var firstName: String

    @Column(name = "last_name", nullable = false, length = 150)
    lateinit var lastName: String

    @Column(name = "middle_name", length = 150)
    var middleName: String? = null

    @Column(name = "role_type", nullable = false, length = 100)
    lateinit var roleType: String
    // e.g., DOCTOR, NURSE, CLEANER, ADMIN, PHARMACIST

    @Column(name = "specializations", columnDefinition = "jsonb") // PostgreSQL-specific
    @Convert(converter = StringListConverter::class)
    @JdbcTypeCode(SqlTypes.JSON)
    var specializations: List<String> = listOf()


    @Column(name = "primary_specialization")
    var primarySpecialization: String? = null

    @Column(name = "qualification", length = 150)
    var qualification: String? = null

    @Column(name = "gender", nullable = false, length = 20)
    lateinit var gender: String

    @Column(name = "date_of_birth", nullable = false)
    lateinit var dateOfBirth: LocalDate

    @Column(name = "age", nullable = false, length = 10)
    var age: String = "0"

    @Column(name = "mobile_number", length = 15)
    var mobileNumber: String? = null

    @Column(name = "email", length = 100)
    var email: String? = null

    @Column(name = "registration_number", length = 50, unique = true)
    var registrationNumber: String? = null
    // Optional, applies to licensed professionals

    @Column(name = "registration_state", length = 50)
    var registrationState: String? = null

    @Column(name = "years_of_experience")
    var yearsOfExperience: Int? = null

    @Column(name = "telemedicine_ready")
    var telemedicineReady: Boolean? = null


    @Convert(converter = StringListConverter::class)
    @Column(name = "language")
    @JdbcTypeCode(SqlTypes.JSON)
    var languagesSpoken: List<String> = listOf()

    @Column(name = "is_active", nullable = false)
    var isActive: Boolean = true

    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "staff_id")
    var addresses: MutableList<Address> = mutableListOf()

    @Column(name = "facility_id", nullable = false)
    lateinit var facilityId: String

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime? = null
}
